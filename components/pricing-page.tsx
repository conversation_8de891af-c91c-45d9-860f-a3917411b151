"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, <PERSON>rkles, ChevronDown } from "lucide-react"
import { useRouter } from "next/navigation"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

export default function PricingPage() {
  const router = useRouter()

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleGetStarted = (plan: string) => {
    if (plan === 'free') {
      router.push("/signup")
    } else if (plan === 'pro' || plan === 'plus') {
      // Redirect to PayPal checkout page
      router.push(`/checkout/${plan}`)
    }
  }



  const pricingPlans = [
    {
      name: "Free",
      price: 0,
      badge: "FREE",
      badgeColor: "bg-gray-600",
      description: "Perfect for trying out our platform",
      jobsPerMonth: 3,
      resumesPerJob: 50,
      totalResumes: 150,
      dataRetention: "60 days",
      features: [
        "3 jobs per month",
        "50 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Delete Resume after 60 days",
        "1 user only",
      ],
      buttonText: "Start Free Plan",
      buttonStyle: "border border-gray-600 text-gray-300 hover:text-white hover:border-[#1aa8e0] bg-transparent",
    },
    {
      name: "Pro",
      price: 6.69,
      badge: "POPULAR",
      badgeColor: "bg-[#1aa8e0]",
      description: "Perfect for growing HR teams",
      jobsPerMonth: 50,
      resumesPerJob: 100,
      totalResumes: 5000,
      dataRetention: "180 days",
      features: [
        "50 jobs per month",
        "100 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Priority support",
        "Delete Resume after 180 days",
        "1 user only",
      ],
      buttonText: "Start Pro Plan",
      buttonStyle: "bg-[#1aa8e0] text-white hover:bg-[#1aa8e0]/90 border-0",
    },
    {
      name: "Plus",
      price: 12.99,
      badge: "BEST VALUE",
      badgeColor: "bg-[#8529db]",
      description: "Perfect for bulk hiring",
      jobsPerMonth: 100,
      resumesPerJob: 100,
      totalResumes: 10000,
      dataRetention: "180 days",
      features: [
        "100 jobs per month",
        "100 resumes per job",
        "AI Screening",
        "CSV Export",
        "Resume Database",
        "Priority support",
        "Delete Resume after 180 days",
        "1 user only",
      ],
      buttonText: "Start Plus Plan",
      buttonStyle: "bg-[#8529db] text-white hover:bg-[#8529db]/90 border-0 hover:border-[#1aa8e0]",
      isHighlighted: true,
    },
  ]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#8529db] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#1aa8e0] rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-[#64738b] rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Header */}
      <header className="relative z-20 flex items-center justify-between px-8 py-6 lg:px-16">
        <div className="flex items-center gap-3 cursor-pointer" onClick={scrollToTop}>
          <div className="w-10 h-10 flex items-center justify-center">
            <img src="/images/the-consult-now-logo.png" alt="The Consult Now Logo" className="w-10 h-10 object-contain" />
          </div>
          <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
        </div>

        <nav className="flex items-center gap-6">
          <a href="/#about" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            About
          </a>
          <a href="/blog" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Blog
          </a>
          <a href="/pricing" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Pricing
          </a>
          <a href="/signup" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
            Log in / Sign up
          </a>
        </nav>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 py-20">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6">
            Simple,{" "}
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Transparent
            </span>{" "}
            Pricing
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed mb-8">
            Choose the perfect plan for your hiring needs. Start free and scale as you grow.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="relative z-10 max-w-7xl mx-auto px-8 mb-20">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 justify-center">
          {pricingPlans.map((plan, index) => (
            <div
              key={plan.name}
              className={`bg-black/40 backdrop-blur-xl border border-gray-700/30 hover:border-[#1aa8e0]/60 rounded-2xl p-8 relative transition-all duration-300 hover:scale-105 ${
                plan.isHighlighted ? 'ring-2 ring-[#1aa8e0]/50 scale-105' : ''
              }`}
            >
              {/* Badge */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className={`${plan.badgeColor} text-white border-0 px-4 py-1`}>{plan.badge}</Badge>
              </div>

              {/* Plan Header */}
              <div className="text-center mb-8 mt-4">
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 text-sm mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-white">
                    {plan.price === 0 ? 'Free' : `$${plan.price}`}
                  </span>
                  {plan.price > 0 && (
                    <span className="text-gray-400 text-lg">/month</span>
                  )}
                </div>
              </div>

              {/* Usage Stats */}
              <div className="bg-black/30 rounded-lg p-4 mb-6">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-[#1aa8e0]">{plan.jobsPerMonth}</div>
                    <div className="text-xs text-gray-400">Jobs/Month</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-[#8529db]">{plan.resumesPerJob}</div>
                    <div className="text-xs text-gray-400">Resumes/Job</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-white">{plan.totalResumes.toLocaleString()}</div>
                    <div className="text-xs text-gray-400">Total Resumes</div>
                  </div>
                </div>
              </div>



              {/* Features */}
              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-[#1aa8e0] flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <Button
                className={`w-full py-3 ${plan.buttonStyle}`}
                onClick={() => handleGetStarted(plan.name.toLowerCase())}
              >
                {plan.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 max-w-4xl mx-auto px-8 mb-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">
            Frequently Asked{" "}
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
        </div>

        <Accordion type="single" collapsible className="space-y-4">
          <AccordionItem value="item-1" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              What happens if I exceed my limits?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              We'll notify you when you're approaching your limits. The Free plan includes 3 jobs per month with up to 50 resumes per job.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-2" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              How many users can I add to my plan?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              The Free plan includes 1 user only and is designed for individual HR professionals.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-3" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              How long do you keep my resume data?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              Resume data is automatically deleted after 60 days for security and privacy.
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="item-4" className="bg-black/40 backdrop-blur-xl border border-gray-700/30 rounded-xl px-6">
            <AccordionTrigger className="text-lg font-semibold text-white hover:text-[#1aa8e0] hover:no-underline">
              Is the platform completely free?
            </AccordionTrigger>
            <AccordionContent className="text-gray-400 pb-6">
              Yes! Our platform is completely free and allows you to analyze up to 3 jobs per month with up to 50 resumes per job at no cost.
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-black/60 backdrop-blur-xl border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-4 cursor-pointer" onClick={scrollToTop}>
                <div className="w-10 h-10 flex items-center justify-center">
                  <img src="/images/the-consult-now-logo.png" alt="The Consult Now Logo" className="w-10 h-10 object-contain" />
                </div>
                <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed max-w-md">
                Cut manual screening by 99% and find the perfect candidates instantly.
              </p>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Home
                  </a>
                </li>
                <li>
                  <a href="/pricing" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>

            {/* Policy Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Policy</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-gray-800 mt-8 pt-6">
            <p className="text-gray-500 text-sm text-center">© 2025 The Consult Now. All rights reserved.</p>
          </div>
        </div>
      </footer>



      <style jsx>{`
        .clean-box {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </div>
  )
}
