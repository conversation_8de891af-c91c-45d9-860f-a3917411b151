"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, FileText, Upload, BarChart3 } from "lucide-react"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

export default function NeonHeroBox() {
  const [lineProgress, setLineProgress] = useState(0)
  const [resumesPerMonth, setResumesPerMonth] = useState(100)
  const [screeningTime, setScreeningTime] = useState(5)
  const router = useRouter()

  useEffect(() => {
    const interval = setInterval(() => {
      setLineProgress((prev) => (prev + 1) % 200) // 200 steps for smooth animation
    }, 50) // Update every 50ms for smooth animation

    return () => clearInterval(interval)
  }, [])

  // Calculate which segments should be drawn
  const firstLineProgress = Math.min(lineProgress, 100) / 100
  const secondLineProgress = Math.max(0, Math.min(lineProgress - 100, 100)) / 100

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
  }

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  const handleSignUpClick = () => {
    router.push("/signup")
  }

  const handleStartBetaClick = () => {
    router.push("/signup")
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-72 h-72 bg-[#8529db] rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#1aa8e0] rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-[#64738b] rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Hero Box */}
      <div id="about" className="relative z-10 max-w-7xl w-full mx-auto">
        <div className="clean-box bg-black/40 backdrop-blur-xl border-l-2 border-r-2 border-b-2 border-[#1aa8e0]/50 rounded-b-3xl shadow-2xl relative overflow-hidden h-[84vh] flex flex-col">
          {/* Header inside rectangle at the top */}
          <header className="relative z-20 flex items-center justify-between px-8 py-6 lg:px-16">
            {/* Logo on left */}
            <div className="flex items-center gap-3 cursor-pointer" onClick={scrollToTop}>
              <div className="w-10 h-10 flex items-center justify-center">
                <img src="/images/the-consult-now-logo.png" alt="The Consult Now Logo" className="w-10 h-10 object-contain" />
              </div>
              <span className="text-white font-bold text-xl tracking-wide">The Consult Now</span>
            </div>

            {/* Navigation on right */}
            <nav className="flex items-center gap-6">
              <button
                onClick={() => scrollToSection("about")}
                className="text-gray-300 hover:text-white transition-colors cursor-pointer"
              >
                About
              </button>
              <a href="/blog" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                Blog
              </a>
              <a href="/pricing" className="text-gray-300 hover:text-white transition-colors cursor-pointer">
                Pricing
              </a>
              <button
                onClick={handleSignUpClick}
                className="text-gray-300 hover:text-white transition-colors cursor-pointer"
              >
                Log in / Sign up
              </button>
            </nav>
          </header>

          {/* Fade effect specifically for the top edge and upper vertical lines */}
          <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-black to-transparent pointer-events-none"></div>

          {/* Fade the upper portion of left vertical line */}
          <div className="absolute top-0 left-0 w-4 h-40 bg-gradient-to-b from-black via-black/70 to-transparent pointer-events-none"></div>

          {/* Fade the upper portion of right vertical line */}
          <div className="absolute top-0 right-0 w-4 h-40 bg-gradient-to-b from-black via-black/70 to-transparent pointer-events-none"></div>

          {/* Corner blending for smooth transition */}
          <div className="absolute top-0 left-0 w-8 h-8 bg-black pointer-events-none"></div>
          <div className="absolute top-0 right-0 w-8 h-8 bg-black pointer-events-none"></div>

          {/* Main content area */}
          <div className="relative z-10 flex-1 flex flex-col justify-center text-center space-y-10 px-8 md:px-16 lg:px-24">
            {/* Beta Badge - No neon effects */}
            <div className="flex justify-center">
              <Badge className="bg-gradient-to-r from-[#8529db]/30 to-[#1aa8e0]/30 text-white border border-[#1aa8e0]/50 px-6 py-3 text-sm backdrop-blur-sm">
                <Sparkles className="w-4 h-4 mr-2" />
                Trusted by 100+ HR's
              </Badge>
            </div>

            {/* Main Headline - No neon effects */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
              We cut manual screening by 99%.
            </h1>

            {/* Subtext - No neon effects */}
            <div className="space-y-2">
              <p className="text-lg md:text-xl text-gray-200 leading-relaxed max-w-4xl mx-auto">
                One AI Powered platform to instantly screen resumes—no matter where they come from.
              </p>
            </div>

            {/* CTA Button - Solid blue like the attached image */}
            <div className="flex justify-center items-center pt-6">
              <Button
                size="lg"
                onClick={handleStartBetaClick}
                className="bg-[#1aa8e0] hover:bg-[#1aa8e0]/90 text-white px-8 py-4 text-lg gap-2 min-w-[200px] border-0"
              >
                <Zap className="w-5 h-5" />
                Start Free Plan
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <section className="relative z-10 max-w-7xl w-full mx-auto py-20 px-8 mt-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl mb-4">
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              How It Works?
            </span>
          </h2>
        </div>

        <div className="flex justify-center max-w-2xl mx-auto">
          <div className="relative">
            {/* Vertical Timeline Line */}
            <div className="absolute left-4 top-8 bottom-8 w-1 bg-gray-600">
              {/* Animated line segments */}
              <div
                className="absolute top-0 left-0 w-full bg-[#8529db] transition-all duration-75 ease-linear"
                style={{ height: `${firstLineProgress * 50}%` }}
              />
              <div
                className="absolute left-0 w-full bg-[#8529db] transition-all duration-75 ease-linear"
                style={{
                  top: "50%",
                  height: `${secondLineProgress * 50}%`,
                }}
              />
            </div>

            {/* Timeline dots */}
            <div className="absolute left-2 top-8 w-5 h-5 bg-[#8529db] rounded-full border-2 border-[#8529db]"></div>
            <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-5 h-5 bg-[#8529db] rounded-full border-2 border-[#8529db]"></div>
            <div className="absolute left-2 bottom-8 w-5 h-5 bg-[#8529db] rounded-full border-2 border-[#8529db]"></div>

            {/* Steps */}
            <div className="flex flex-col space-y-32 pl-16">
              {/* Step A */}
              <div className="flex items-start space-x-6">
                <div className="w-16 h-16 rounded-full bg-[#8529db] flex items-center justify-center flex-shrink-0">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl text-foreground mb-3">Define Requirements</h3>
                  <p className="text-muted-foreground text-base leading-relaxed">
                    Tell us what you need.<br />
                    Simply enter job details like role, skills, and experience level. Our AI uses this to find perfect candidate matches.
                  </p>
                </div>
              </div>

              {/* Step B */}
              <div className="flex items-start space-x-6">
                <div className="w-16 h-16 rounded-full bg-[#8529db] flex items-center justify-center flex-shrink-0">
                  <Upload className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl text-foreground mb-3">Upload Resumes</h3>
                  <p className="text-muted-foreground text-base leading-relaxed">
                    Drag, drop, or bulk upload.<br />
                    Add resumes in any format. Our system parses and organizes them instantly.
                  </p>
                </div>
              </div>

              {/* Step C */}
              <div className="flex items-start space-x-6">
                <div className="w-16 h-16 rounded-full bg-[#8529db] flex items-center justify-center flex-shrink-0">
                  <BarChart3 className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl text-foreground mb-3">Get AI-Powered Matches</h3>
                  <p className="text-muted-foreground text-base leading-relaxed">
                    Smart rankings in seconds.<br />
                    Our AI analyzes resumes against your requirements, scoring candidates by fit with final verdict. Export top picks with one click!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Comparison Table Section */}
      <section className="relative z-10 max-w-7xl w-full mx-auto py-20 px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl mb-4">
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              95% Faster Screening = 10X More Productivity?
            </span>
          </h2>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-background/40 backdrop-blur-xl border border-border rounded-2xl overflow-hidden">
            <div className="grid grid-cols-3 gap-0">
              {/* Header */}
              <div className="bg-[#8529db]/20 p-6 border-b border-border">
                <h3 className="text-foreground font-semibold text-lg">Feature</h3>
              </div>
              <div className="bg-[#8529db]/20 p-6 border-b border-l border-border">
                <h3 className="text-foreground font-semibold text-lg">Manual Screening</h3>
              </div>
              <div className="bg-[#1aa8e0]/20 p-6 border-b border-l border-border">
                <h3 className="text-foreground font-semibold text-lg">The Consult Now (AI)</h3>
              </div>

              {/* Time per resume */}
              <div className="p-6 border-b border-border">
                <p className="text-muted-foreground">Time per resume</p>
              </div>
              <div className="p-6 border-b border-l border-border">
                <p className="text-muted-foreground">5-10 minutes</p>
              </div>
              <div className="p-6 border-b border-l border-border">
                <p className="text-[#1aa8e0] font-semibold">5 seconds with 99% accuracy</p>
              </div>

              {/* Bulk processing */}
              <div className="p-6">
                <p className="text-muted-foreground">Bulk processing</p>
              </div>
              <div className="p-6 border-l border-border">
                <p className="text-muted-foreground">Not scalable</p>
              </div>
              <div className="p-6 border-l border-border">
                <p className="text-[#1aa8e0] font-semibold">100+ resumes in minutes</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* ROI Calculator Section */}
      <section className="relative z-10 max-w-7xl w-full mx-auto py-20 px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl mb-4">
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              How Many Hours Could You Save?
            </span>
          </h2>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-background/40 backdrop-blur-xl border border-border rounded-2xl p-8">
            <div className="space-y-6">
              {/* Resumes per month input */}
              <div>
                <label className="block text-foreground font-medium mb-3">Resumes per month</label>
                <div className="relative">
                  <input
                    type="range"
                    min="50"
                    max="5000"
                    step="50"
                    value={resumesPerMonth}
                    onChange={(e) => setResumesPerMonth(Number(e.target.value))}
                    className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
                  />
                  <div className="flex justify-between text-sm text-gray-400 mt-2">
                    <span>50</span>
                    <span className="text-[#1aa8e0] font-semibold">{resumesPerMonth}</span>
                    <span>5,000</span>
                  </div>
                </div>
              </div>

              {/* Screening time input */}
              <div>
                <label className="block text-foreground font-medium mb-3">Current screening time per resume</label>
                <div className="flex gap-4">
                  {[3, 5, 10].map((time) => (
                    <button
                      key={time}
                      onClick={() => setScreeningTime(time)}
                      className={`flex-1 py-3 px-4 rounded-lg border transition-all ${
                        screeningTime === time
                          ? 'bg-[#1aa8e0] border-[#1aa8e0] text-white'
                          : 'bg-transparent border-border text-muted-foreground hover:border-[#1aa8e0]'
                      }`}
                    >
                      {time} min
                    </button>
                  ))}
                </div>
              </div>

              {/* Results */}
              <div className="bg-[#1aa8e0]/10 border border-[#1aa8e0]/30 rounded-lg p-6 mt-8">
                <div className="text-center">
                  <p className="text-foreground text-lg mb-2">
                    You'll save{' '}
                    <span className="text-[#1aa8e0] font-bold text-2xl">
                      {Math.round((resumesPerMonth * screeningTime - resumesPerMonth * (5/60)) / 60 * 10) / 10}
                    </span>{' '}
                    hours — that's{' '}
                    <span className="text-[#1aa8e0] font-bold">
                      {Math.round((resumesPerMonth * screeningTime - resumesPerMonth * (5/60)) / 60 / 8 * 10) / 10}
                    </span>{' '}
                    full workdays!
                  </p>
                  <Button
                    onClick={() => router.push("/signup")}
                    className="bg-[#1aa8e0] hover:bg-[#1aa8e0]/90 text-white px-8 py-3 mt-4"
                  >
                    Start saving time →
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="relative z-10 max-w-7xl w-full mx-auto py-10 px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl mb-4">
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Features You'll Get
            </span>
          </h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
          {/* Feature 1 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#8529db]/30 rounded-lg p-6 hover:border-[#8529db]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">AI-Powered Resume Screening</h3>
          </div>

          {/* Feature 2 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#1aa8e0]/30 rounded-lg p-6 hover:border-[#1aa8e0]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">Bulk Resume Upload</h3>
          </div>

          {/* Feature 3 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#8529db]/30 rounded-lg p-6 hover:border-[#8529db]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">Interactive Dashboard</h3>
          </div>

          {/* Feature 4 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#1aa8e0]/30 rounded-lg p-6 hover:border-[#1aa8e0]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">Source-Agnostic Compatibility</h3>
          </div>

          {/* Feature 5 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#8529db]/30 rounded-lg p-6 hover:border-[#1aa8e0]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">Central Resume Database</h3>
          </div>

          {/* Feature 6 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#1aa8e0]/30 rounded-lg p-6 hover:border-[#1aa8e0]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">Candidates Results in CSV</h3>
          </div>

          {/* Feature 7 */}
          <div className="bg-background/40 backdrop-blur-xl border border-[#8529db]/30 rounded-lg p-6 hover:border-[#8529db]/60 transition-all duration-300 h-24 flex items-center justify-center">
            <h3 className="text-foreground text-sm font-medium text-center">many more coming soon...</h3>
          </div>
        </div>
      </section>

      {/* Built by HR Section */}
      <section className="relative z-10 max-w-7xl w-full mx-auto py-20 px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl mb-4">
            <span className="bg-gradient-to-r from-[#8529db] to-[#1aa8e0] bg-clip-text text-transparent">
              Built by an HR, for an HR
            </span>
          </h2>
        </div>

        <div className="max-w-4xl mx-auto text-center space-y-6">
          <p className="text-lg text-muted-foreground leading-relaxed">
            We've been in your shoes — staring at endless resumes, juggling tight deadlines and still hoping to find
            that one perfect candidate. It's exhausting. And frankly, it's not the best use of your time.
          </p>
          <p className="text-lg text-muted-foreground leading-relaxed">
            That's exactly why we created The Consult Now. We designed it to take the weight off your shoulders — to remove
            the repetitive, manual screening and replace it with fast, intelligent resume matching that actually works.
          </p>
          <p className="text-lg text-muted-foreground leading-relaxed">
            Our AI doesn't just scan resumes. It understands them. It reads between the lines, aligns candidate
            strengths with your job requirements, and brings you the best fits — in seconds.
          </p>
          <p className="text-lg text-muted-foreground leading-relaxed">
            We're not just saving time — we're giving you the space to focus on what really matters: connecting with
            great people and building strong teams.
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 bg-background/60 backdrop-blur-xl border-t border-border mt-10">
        <div className="max-w-7xl mx-auto px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-4 cursor-pointer" onClick={scrollToTop}>
                <div className="w-10 h-10 flex items-center justify-center">
                  <img src="/images/the-consult-now-logo.png" alt="The Consult Now Logo" className="w-10 h-10 object-contain" />
                </div>
                <span className="text-foreground font-bold text-xl tracking-wide">The Consult Now</span>
              </div>
              <p className="text-muted-foreground text-sm leading-relaxed max-w-md">
                Cut manual screening by 99% and find the perfect candidates instantly.
              </p>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li>
                  <button
                    onClick={() => scrollToSection("features")}
                    className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer"
                  >
                    Features
                  </button>
                </li>
                <li>
                  <a href="/pricing" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Pricing
                  </a>
                </li>
                <li>
                  <a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Blog
                  </a>
                </li>
                <li>
                  <button
                    onClick={() => scrollToSection("about")}
                    className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer"
                  >
                    About
                  </button>
                </li>
              </ul>
            </div>

            {/* Policy Links */}
            <div>
              <h3 className="text-white font-semibold mb-4">Policy</h3>
              <ul className="space-y-2">
                <li>
                  <a href="/privacy-policy" className="text-gray-400 hover:text-white transition-colors text-sm cursor-pointer">
                    Privacy Policy
                  </a>
                </li>
              </ul>
            </div>


          </div>

          {/* Bottom Section */}
          <div className="border-t border-border mt-8 pt-6">
            <p className="text-muted-foreground text-sm text-center">© 2025 The Consult Now. All rights reserved.</p>
          </div>
        </div>
      </footer>

      <style jsx>{`
        .clean-box {
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #1aa8e0;
          cursor: pointer;
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #1aa8e0;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  )
}