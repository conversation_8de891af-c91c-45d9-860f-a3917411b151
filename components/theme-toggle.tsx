'use client'

import * as React from 'react'
import { useTheme } from 'next-themes'
import { Sun, Moon } from 'lucide-react'
import { Button } from '@/components/ui/button'

interface ThemeToggleProps {
  position?: 'fixed' | 'inline'
  className?: string
}

export function ThemeToggle({ position = 'fixed', className = '' }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // Ensure we only render theme switching in the client to avoid hydration mismatch
  React.useEffect(() => {
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark')
  }

  const buttonContent = (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleTheme}
      className={`h-12 w-12 rounded-full bg-background/80 backdrop-blur-sm border-2 shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 ${className}`}
      title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {mounted ? (
        theme === 'dark' ? (
          <Sun className="h-5 w-5 text-yellow-500" />
        ) : (
          <Moon className="h-5 w-5 text-blue-600" />
        )
      ) : (
        <Sun className="h-5 w-5" />
      )}
    </Button>
  )

  if (position === 'fixed') {
    return (
      <>
        {/* Right side toggle */}
        <div className="fixed bottom-6 right-6 z-50">
          {buttonContent}
        </div>
        {/* Bottom center toggle */}
        <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
          {buttonContent}
        </div>
      </>
    )
  }

  return buttonContent
}
