"use client"

import * as React from "react"
import { usePathname } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"

interface AppLayoutProps {
  children: React.ReactNode
}

// Pages that should not show the sidebar
const publicPages = [
  "/",
  "/login",
  "/forgot-password",
  "/signup",
  "/blog",
  "/pricing",
  "/privacy-policy"
]

export function AppLayout({ children }: AppLayoutProps) {
  const pathname = usePathname()

  // Check if current page should show sidebar
  // Include all authenticated pages except public ones and blog posts
  const shouldShowSidebar = !publicPages.includes(pathname) &&
    !pathname.startsWith("/blog/") &&
    !pathname.startsWith("/checkout/") &&
    !pathname.startsWith("/_next") &&
    !pathname.startsWith("/api")

  if (!shouldShowSidebar) {
    return <>{children}</>
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Floating Sidebar */}
      <AppSidebar />

      {/* Main content area - no margin needed since sidebar is floating */}
      <main className="min-h-screen">
        {children}
      </main>
    </div>
  )
}
