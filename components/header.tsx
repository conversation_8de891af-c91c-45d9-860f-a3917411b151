"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { LogOut, Database, Upload, FileText } from "lucide-react"
import { auth } from "@/lib/firebase"
import { signOut } from "firebase/auth"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

export function Header() {
  const router = useRouter()
  const { toast } = useToast()

  const handleSignOut = async () => {
    try {
      await signOut(auth)
      toast({
        title: "Signed out successfully",
        description: "You have been signed out of your account.",
      })
      router.push("/login")
    } catch (error) {
      console.error("Error signing out:", error)
      toast({
        title: "Error signing out",
        description: "There was a problem signing out. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <header className="bg-black border-b border-gray-800 py-4">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link href="/" className="flex items-center gap-3">
            <div className="w-10 h-10 flex items-center justify-center">
              <img src="/images/the-consult-now-logo.png" alt="The Consult Now Logo" className="w-10 h-10 object-contain" />
            </div>
            <span className="text-xl font-bold text-white">The Consult Now</span>
          </Link>
          <nav className="hidden md:flex space-x-4">
            <Link href="/job-description-form" className="text-gray-300 hover:text-white">
              <Button variant="ghost" className="flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                Job Description
              </Button>
            </Link>
            <Link href="/resume-upload" className="text-gray-300 hover:text-white">
              <Button variant="ghost" className="flex items-center">
                <Upload className="mr-2 h-4 w-4" />
                Resume Upload
              </Button>
            </Link>
            <Link href="/resume-dashboard" className="text-gray-300 hover:text-white">
              <Button variant="ghost" className="flex items-center">
                <Database className="mr-2 h-4 w-4" />
                Dashboard
              </Button>
            </Link>
          </nav>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleSignOut}>
            <LogOut className="h-4 w-4" />
            <span className="ml-2 hidden md:inline">Sign Out</span>
          </Button>
        </div>
      </div>
    </header>
  )
}
